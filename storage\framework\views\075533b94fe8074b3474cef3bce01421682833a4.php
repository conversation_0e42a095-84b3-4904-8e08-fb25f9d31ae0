

<?php if(isset($category_id)): ?>
    <?php
        $meta_title = $category->meta_title;
        $meta_description = $category->meta_description;
    ?>
<?php elseif(isset($brand_id)): ?>
    <?php
        $meta_title = get_single_brand($brand_id)->meta_title;
        $meta_description = get_single_brand($brand_id)->meta_description;
    ?>
<?php else: ?>
    <?php
        $meta_title         = get_setting('meta_title');
        $meta_description   = get_setting('meta_description');
    ?>
<?php endif; ?>

<?php
    $product_count = get_products_count()
?>

<?php $__env->startSection('meta_title'); ?><?php echo e($meta_title); ?><?php $__env->stopSection(); ?>
<?php $__env->startSection('meta_description'); ?><?php echo e($meta_description); ?><?php $__env->stopSection(); ?>

<?php $__env->startSection('meta'); ?>
    <!-- Schema.org markup for Google+ -->
    <meta itemprop="name" content="<?php echo e($meta_title); ?>">
    <meta itemprop="description" content="<?php echo e($meta_description); ?>">

    <!-- Twitter Card data -->
    <meta name="twitter:title" content="<?php echo e($meta_title); ?>">
    <meta name="twitter:description" content="<?php echo e($meta_description); ?>">

    <!-- Open Graph data -->
    <meta property="og:title" content="<?php echo e($meta_title); ?>" />
    <meta property="og:description" content="<?php echo e($meta_description); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

    <section class="shop__section section--padding">
        <div class="container-fluid">
            <form class="" id="search-form" action="" method="GET">
                <?php if(request('keyword')): ?>
                    <input type="hidden" name="keyword" value="<?php echo e(request('keyword')); ?>">
                <?php endif; ?>
                <?php if(request('location')): ?>
                    <input type="hidden" name="location" value="<?php echo e(request('location')); ?>">
                <?php endif; ?>
                <?php if(request('latitude')): ?>
                        <input type="hidden" name="latitude" value="<?php echo e(request('latitude')); ?>">
                    <?php endif; ?>
                    <?php if(request('longitude')): ?>
                        <input type="hidden" name="longitude" value="<?php echo e(request('longitude')); ?>">
                    <?php endif; ?>
                <div class="shop__header bg__gray--color d-flex align-items-center justify-content-between mb-30">
                    <button type="button" class="widget__filter--btn d-flex d-lg-none align-items-center" data-offcanvas="">
                        <svg class="widget__filter--btn__icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="28" d="M368 128h80M64 128h240M368 384h80M64 384h240M208 256h240M64 256h80"></path><circle cx="336" cy="128" r="28" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="28"></circle><circle cx="176" cy="256" r="28" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="28"></circle><circle cx="336" cy="384" r="28" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="28"></circle></svg>
                        <span class="widget__filter--btn__text"><?php echo e(translate('Filters')); ?></span>
                    </button>
                    <div class="product__view--mode d-flex align-items-center">
                        <div class="product__view--mode__list product__short--by align-items-center d-none d-lg-flex">
                            <label class="product__view--label">Sort By :</label>
                            <div class="select shop__header--select">
                                <select class="product__view--select" name="sort_by" onchange="filter()">
                                    <option value=""><?php echo e(translate('Sort by')); ?></option>
                                    <option value="newest" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'newest'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Newest')); ?></option>
                                    <option value="oldest" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'oldest'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Oldest')); ?></option>
                                    <option value="price-asc" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'price-asc'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Price low to high')); ?></option>
                                    <option value="price-desc" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'price-desc'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Price high to low')); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="product__view--mode__list">
                            <div class="product__grid--column__buttons d-flex justify-content-center">
                                <button type="button" class="product__grid--column__buttons--icons active" aria-label="product grid button" data-toggle="tab" data-target="#product_grid">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 9 9">
                                        <g transform="translate(-1360 -479)">
                                            <rect id="Rectangle_5725" data-name="Rectangle 5725" width="4" height="4" transform="translate(1360 479)" fill="currentColor"></rect>
                                            <rect id="Rectangle_5727" data-name="Rectangle 5727" width="4" height="4" transform="translate(1360 484)" fill="currentColor"></rect>
                                            <rect id="Rectangle_5726" data-name="Rectangle 5726" width="4" height="4" transform="translate(1365 479)" fill="currentColor"></rect>
                                            <rect id="Rectangle_5728" data-name="Rectangle 5728" width="4" height="4" transform="translate(1365 484)" fill="currentColor"></rect>
                                        </g>
                                    </svg>
                                </button>
                                <button type="button" class="product__grid--column__buttons--icons" aria-label="product list button" data-toggle="tab" data-target="#product_list">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 13 8">
                                        <g id="Group_14700" data-name="Group 14700" transform="translate(-1376 -478)">
                                            <g transform="translate(12 -2)">
                                                <g id="Group_1326" data-name="Group 1326">
                                                    <rect id="Rectangle_5729" data-name="Rectangle 5729" width="3" height="2" transform="translate(1364 483)" fill="currentColor"></rect>
                                                    <rect id="Rectangle_5730" data-name="Rectangle 5730" width="9" height="2" transform="translate(1368 483)" fill="currentColor"></rect>
                                                </g>
                                                <g id="Group_1328" data-name="Group 1328" transform="translate(0 -3)">
                                                    <rect id="Rectangle_5729-2" data-name="Rectangle 5729" width="3" height="2" transform="translate(1364 483)" fill="currentColor"></rect>
                                                    <rect id="Rectangle_5730-2" data-name="Rectangle 5730" width="9" height="2" transform="translate(1368 483)" fill="currentColor"></rect>
                                                </g>
                                                <g id="Group_1327" data-name="Group 1327" transform="translate(0 -1)">
                                                    <rect id="Rectangle_5731" data-name="Rectangle 5731" width="3" height="2" transform="translate(1364 487)" fill="currentColor"></rect>
                                                    <rect id="Rectangle_5732" data-name="Rectangle 5732" width="9" height="2" transform="translate(1368 487)" fill="currentColor"></rect>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <?php if(get_setting('vendor_system_activation') == 1): ?>
                            <div class="product__view--mode__list product__short--by align-items-center d-none d-lg-flex">
                                <label class="product__view--label">Search By :</label>
                                <div class="select shop__header--select">
                                    <select class="product__view--select" name="search_by" onchange="filter()">
                                        <option value="products" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'products'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Products')); ?></option>
                                        <option value="shops" <?php if(isset($sort_by)): ?> <?php if($sort_by == 'shops'): ?> selected <?php endif; ?> <?php endif; ?>><?php echo e(translate('Shops')); ?></option>
                                    </select>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                </div>
                <div class="row">
                    <div class="col-xl-3 col-lg-4">
                        <div class="shop__sidebar--widget widget__area d-none d-lg-block">
                            <div class="single__widget widget__bg">
                                <h2 class="widget__title h3"><?php echo e(translate('Categories')); ?></h2>
                                <ul class="widget__categories--menu">
                                    <?php if(!isset($category_id)): ?>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <a href="<?php echo e(route('products.category', $category->slug)); ?>">
                                                <li class="widget__categories--menu__list">
                                                    <label class="widget__categories--menu__label d-flex align-items-center">
                                                        <span class="widget__categories--menu__text"><?php echo e($category->getTranslation('name')); ?></span>
                                                    </label>
                                                </li>
                                            </a>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <a class="text-reset fs-14 w-100 hov-text-primary" href="<?php echo e(route('search')); ?>">
                                            <li class="widget__categories--menu__list py-2 px-4">
                                                <i class="las la-angle-left"></i>
                                                <?php echo e(translate('All Categories')); ?>

                                            </li>
                                        </a>

                                        <li class="widget__categories--menu__list">
                                            <label class="widget__categories--menu__label d-flex align-items-center">
                                                <span class="widget__categories--menu__text"><?php echo e($category->getTranslation('name')); ?></span>
                                                <?php if(!empty($category->childrenCategories) && count($category->childrenCategories) > 0): ?>
                                                    <svg class="widget__categories--menu__arrowdown--icon" xmlns="http://www.w3.org/2000/svg" width="12.355" height="8.394">
                                                        <path d="M15.138,8.59l-3.961,3.952L7.217,8.59,6,9.807l5.178,5.178,5.178-5.178Z" transform="translate(-6 -8.59)" fill="currentColor"></path>
                                                    </svg>
                                                <?php endif; ?>
                                            </label>
                                            <?php if(!empty($category->childrenCategories) && count($category->childrenCategories) > 0): ?>
                                                <ul class="widget__categories--sub__menu" style="display: none; box-sizing: border-box;">
                                                    <?php $__currentLoopData = $category->childrenCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $immediate_children_category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <li class="widget__categories--sub__menu--list">
                                                            <a class="widget__categories--sub__menu--link d-flex align-items-center" href="<?php echo e(route('products.category', $immediate_children_category->slug)); ?>">
                                                                <span class="widget__categories--sub__menu--text"><?php echo e($immediate_children_category->getTranslation('name')); ?></span>
                                                            </a>
                                                        </li>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </ul>
                                            <?php endif; ?>
                                        </li>
                                    <?php endif; ?>

                                </ul>
                            </div>

                            <!-- Attributes -->
                            <?php $__currentLoopData = $attributes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attribute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="single__widget widget__bg">
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between"
                                               data-toggle="collapse" data-target="#collapse_<?php echo e(str_replace(' ', '_', $attribute->name)); ?>" style="white-space: normal;">
                                                <?php echo e($attribute->getTranslation('name')); ?>

                                            </a>
                                        </div>
                                        <?php
                                            $show = '';
                                            foreach ($attribute->attribute_values as $attribute_value){
                                                if(in_array($attribute_value->value, $selected_attribute_values)){
                                                    $show = 'show';
                                                }
                                            }
                                        ?>
                                        <div class="collapse <?php echo e($show); ?>" id="collapse_<?php echo e(str_replace(' ', '_', $attribute->name)); ?>">
                                            <div class="p-3 aiz-checkbox-list">
                                                <?php $__currentLoopData = $attribute->attribute_values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attribute_value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <label class="aiz-checkbox mb-3">
                                                        <input
                                                            type="checkbox"
                                                            name="selected_attribute_values[]"
                                                            value="<?php echo e($attribute_value->value); ?>" <?php if(in_array($attribute_value->value, $selected_attribute_values)): ?> checked <?php endif; ?>
                                                            onchange="filter()"
                                                        >
                                                        <span class="aiz-square-check"></span>
                                                        <span class="fs-14 fw-400 text-dark"><?php echo e($attribute_value->value); ?></span>
                                                    </label>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <!-- Color -->
                            <?php if(get_setting('color_filter_activation')): ?>
                                <div class="single__widget widget__bg">
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#collapse_color">
                                                <?php echo e(translate('Filter by color')); ?>

                                            </a>
                                        </div>
                                        <?php
                                            $show = '';
                                            foreach ($colors as $key => $color){
                                                if(isset($selected_color) && $selected_color == $color->code){
                                                    $show = 'show';
                                                }
                                            }
                                        ?>
                                        <div class="collapse <?php echo e($show); ?>" id="collapse_color">
                                            <div class="p-3 aiz-radio-inline">
                                                <?php $__currentLoopData = $colors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $color): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <label class="aiz-megabox pl-0 mr-2" data-toggle="tooltip" data-title="<?php echo e($color->name); ?>">
                                                        <input
                                                            type="radio"
                                                            name="color"
                                                            value="<?php echo e($color->code); ?>"
                                                            onchange="filter()"
                                                            <?php if(isset($selected_color) && $selected_color == $color->code): ?> checked <?php endif; ?>
                                                        >
                                                        <span class="aiz-megabox-elem rounded d-flex align-items-center justify-content-center p-1 mb-2">
                                                        <span class="size-30px d-inline-block rounded" style="background: <?php echo e($color->code); ?>;"></span>
                                                    </span>
                                                    </label>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="single__widget price__filter widget__bg">
                                <h2 class="widget__title h3">Filter By Price</h2>
                                <form class="price__filter--form" action="#">
                                    <div class="price__filter--form__inner mb-15 d-flex align-items-center">
                                        <div class="price__filter--group">
                                            <label class="price__filter--label" for="Filter-Price-GTE2">From</label>
                                            <div class="price__filter--input border-radius-5 d-flex align-items-center">
                                                <span class="price__filter--currency">₹</span>
                                                <label>
                                                    <input class="price__filter--input__field border-0" name="min_price" value="<?php if(isset($min_price)): ?><?php echo e($min_price); ?><?php endif; ?>" type="number" placeholder="0"
                                                           <?php if($products->min('unit_price') > 0): ?>
                                                               min="<?php echo e($products->min('unit_price')); ?>"
                                                           <?php else: ?>
                                                               min="0"
                                                           <?php endif; ?>
                                                           <?php if($products->max('unit_price') > 0): ?>
                                                               max="<?php echo e($products->max('unit_price')); ?>"
                                                           <?php else: ?>
                                                               max="0"
                                                        <?php endif; ?>
                                                    >
                                                </label>
                                            </div>
                                        </div>
                                        <div class="price__divider">
                                            <span>-</span>
                                        </div>
                                        <div class="price__filter--group">
                                            <label class="price__filter--label" for="Filter-Price-LTE2">To</label>
                                            <div class="price__filter--input border-radius-5 d-flex align-items-center">
                                                <span class="price__filter--currency">₹</span>
                                                <label>
                                                    <input class="price__filter--input__field border-0" name="max_price" value="<?php if(isset($max_price)): ?><?php echo e($max_price); ?><?php endif; ?>" type="number" placeholder="999.00"
                                                           <?php if($products->min('unit_price') > 0): ?>
                                                               min="<?php echo e($products->min('unit_price')); ?>"
                                                           <?php else: ?>
                                                               min="0"
                                                           <?php endif; ?>
                                                           <?php if($products->max('unit_price') > 0): ?>
                                                               max="<?php echo e($products->max('unit_price')); ?>"
                                                           <?php else: ?>
                                                               max="0"
                                                        <?php endif; ?>
                                                    >
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="price__filter--btn primary__btn" type="submit">Filter</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-9 col-lg-8">
                        <div class="shop__product--wrapper">
                            <?php if($products->isNotEmpty()): ?>
                                <div class="tab_content">
                                    <div id="product_grid" class="tab_pane active show">
                                        <div class="product__section--inner product__grid--inner">
                                            <div class="row row-cols-xl-4 row-cols-lg-3 row-cols-md-3 row-cols-2 mb--n30">
                                                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php echo $__env->make('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="product_list" class="tab_pane">
                                        <div class="product__section--inner">
                                            <div class="row row-cols-1 mb--n30">
                                                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php echo $__env->make('frontend.'.get_setting('homepage_select').'.partials.product_box_listing_view',['product' => $product], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="pagination__area bg__gray--color">
                                    <nav class="pagination justify-content-center">
                                        <ul class="pagination__wrapper d-flex align-items-center justify-content-center">
                                            <?php echo e($products->appends(request()->input())->links()); ?>

                                        </ul>
                                    </nav>
                                </div>
                            <?php else: ?>
                                <?php if(get_setting('vendor_system_activation') == 1): ?>
                                    <div class="no-products-message text-center">
                                        <h2 class="mb-3">No products available in this location, try searching for shop</h2>
                                        <a href="#" class="primary__btn quickview__cart--btn" onclick="selectShops()">
                                            <?php echo e(translate('Search by shop')); ?>

                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="no-products-message text-center">
                                        <h2 class="mb-3">No products available for this keyword..</h2>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <div class="offcanvas__filter--sidebar widget__area">
        <button type="button" class="offcanvas__filter--close" data-offcanvas="">
            <svg class="minicart__close--icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M368 368L144 144M368 144L144 368"></path></svg> <span class="offcanvas__filter--close__text">Close</span>
        </button>
        <form class="" id="search-form" action="" method="GET">
            <?php if(request('keyword')): ?>
                <input type="hidden" name="keyword" value="<?php echo e(request('keyword')); ?>">
            <?php endif; ?>
            <?php if(request('location')): ?>
                <input type="hidden" name="location" value="<?php echo e(request('location')); ?>">
            <?php endif; ?>
            <div class="offcanvas__filter--sidebar__inner">
            <div class="single__widget widget__bg">
                <h2 class="widget__title h3">Categories</h2>
                <ul class="widget__categories--menu">
                    <?php if(!isset($category_id)): ?>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(route('products.category', $category->slug)); ?>">
                                <li class="widget__categories--menu__list">
                                    <label class="widget__categories--menu__label d-flex align-items-center">
                                        <span class="widget__categories--menu__text"><?php echo e($category->getTranslation('name')); ?></span>
                                    </label>
                                </li>
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <a class="text-reset fs-14 w-100 hov-text-primary" href="<?php echo e(route('search')); ?>">
                            <li class="widget__categories--menu__list py-2 px-4">
                                <i class="las la-angle-left"></i>
                                <?php echo e(translate('All Categories')); ?>

                            </li>
                        </a>

                        <li class="widget__categories--menu__list">
                            <label class="widget__categories--menu__label d-flex align-items-center">
                                <span class="widget__categories--menu__text"><?php echo e($category->getTranslation('name')); ?></span>
                                <?php if(!empty($category->childrenCategories) && count($category->childrenCategories) > 0): ?>
                                    <svg class="widget__categories--menu__arrowdown--icon" xmlns="http://www.w3.org/2000/svg" width="12.355" height="8.394">
                                        <path d="M15.138,8.59l-3.961,3.952L7.217,8.59,6,9.807l5.178,5.178,5.178-5.178Z" transform="translate(-6 -8.59)" fill="currentColor"></path>
                                    </svg>
                                <?php endif; ?>
                            </label>
                            <?php if(!empty($category->childrenCategories) && count($category->childrenCategories) > 0): ?>
                                <ul class="widget__categories--sub__menu" style="display: none; box-sizing: border-box;">
                                    <?php $__currentLoopData = $category->childrenCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $immediate_children_category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="widget__categories--sub__menu--list">
                                            <a class="widget__categories--sub__menu--link d-flex align-items-center" href="<?php echo e(route('products.category', $immediate_children_category->slug)); ?>">
                                                <span class="widget__categories--sub__menu--text"><?php echo e($immediate_children_category->getTranslation('name')); ?></span>
                                            </a>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            <?php endif; ?>
                        </li>
                    <?php endif; ?>

                </ul>
            </div>
                <!-- Attributes -->
                <?php $__currentLoopData = $attributes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attribute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="single__widget widget__bg">
                        <div class="bg-white border mb-3">
                            <div class="fs-16 fw-700 p-3">
                                <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between"
                                   data-toggle="collapse" data-target="#collapse_<?php echo e(str_replace(' ', '_', $attribute->name)); ?>" style="white-space: normal;">
                                    <?php echo e($attribute->getTranslation('name')); ?>

                                </a>
                            </div>
                            <?php
                                $show = '';
                                foreach ($attribute->attribute_values as $attribute_value){
                                    if(in_array($attribute_value->value, $selected_attribute_values)){
                                        $show = 'show';
                                    }
                                }
                            ?>
                            <div class="collapse <?php echo e($show); ?>" id="collapse_<?php echo e(str_replace(' ', '_', $attribute->name)); ?>">
                                <div class="p-3 aiz-checkbox-list">
                                    <?php $__currentLoopData = $attribute->attribute_values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attribute_value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="aiz-checkbox mb-3">
                                            <input
                                                type="checkbox"
                                                name="selected_attribute_values[]"
                                                value="<?php echo e($attribute_value->value); ?>" <?php if(in_array($attribute_value->value, $selected_attribute_values)): ?> checked <?php endif; ?>
                                                onchange="filter()"
                                            >
                                            <span class="aiz-square-check"></span>
                                            <span class="fs-14 fw-400 text-dark"><?php echo e($attribute_value->value); ?></span>
                                        </label>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <!-- color_filter_activation -->
                <?php if(get_setting('color_filter_activation')): ?>
                    <div class="single__widget widget__bg">
                        <div class="bg-white border mb-3">
                            <div class="fs-16 fw-700 p-3">
                                <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#collapse_color">
                                    <?php echo e(translate('Filter by color')); ?>

                                </a>
                            </div>
                            <?php
                                $show = '';
                                foreach ($colors as $key => $color){
                                    if(isset($selected_color) && $selected_color == $color->code){
                                        $show = 'show';
                                    }
                                }
                            ?>
                            <div class="collapse <?php echo e($show); ?>" id="collapse_color">
                                <div class="p-3 aiz-radio-inline">
                                    <?php $__currentLoopData = $colors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $color): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="aiz-megabox pl-0 mr-2" data-toggle="tooltip" data-title="<?php echo e($color->name); ?>">
                                            <input
                                                type="radio"
                                                name="color"
                                                value="<?php echo e($color->code); ?>"
                                                onchange="filter()"
                                                <?php if(isset($selected_color) && $selected_color == $color->code): ?> checked <?php endif; ?>
                                            >
                                            <span class="aiz-megabox-elem rounded d-flex align-items-center justify-content-center p-1 mb-2">
                                                        <span class="size-30px d-inline-block rounded" style="background: <?php echo e($color->code); ?>;"></span>
                                                    </span>
                                        </label>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="single__widget price__filter widget__bg">
                    <h2 class="widget__title h3">Filter By Price</h2>
                    <form class="price__filter--form" action="#">
                        <div class="price__filter--form__inner mb-15 d-flex align-items-center">
                            <div class="price__filter--group">
                                <label class="price__filter--label" for="Filter-Price-GTE2">From</label>
                                <div class="price__filter--input border-radius-5 d-flex align-items-center">
                                    <span class="price__filter--currency">₹</span>
                                    <label>
                                        <input class="price__filter--input__field border-0" name="min_price" value="<?php if(isset($min_price)): ?><?php echo e($min_price); ?><?php endif; ?>" type="number" placeholder="0"
                                               <?php if($products->min('unit_price') > 0): ?>
                                                   min="<?php echo e($products->min('unit_price')); ?>"
                                               <?php else: ?>
                                                   min="0"
                                               <?php endif; ?>
                                               <?php if($products->max('unit_price') > 0): ?>
                                                   max="<?php echo e($products->max('unit_price')); ?>"
                                               <?php else: ?>
                                                   max="0"
                                            <?php endif; ?>
                                        >
                                    </label>
                                </div>
                            </div>
                            <div class="price__divider">
                                <span>-</span>
                            </div>
                            <div class="price__filter--group">
                                <label class="price__filter--label" for="Filter-Price-LTE2">To</label>
                                <div class="price__filter--input border-radius-5 d-flex align-items-center">
                                    <span class="price__filter--currency">₹</span>
                                    <label>
                                        <input class="price__filter--input__field border-0" name="max_price" value="<?php if(isset($max_price)): ?><?php echo e($max_price); ?><?php endif; ?>" type="number" placeholder="999.00"
                                               <?php if($products->min('unit_price') > 0): ?>
                                                   min="<?php echo e($products->min('unit_price')); ?>"
                                               <?php else: ?>
                                                   min="0"
                                               <?php endif; ?>
                                               <?php if($products->max('unit_price') > 0): ?>
                                                   max="<?php echo e($products->max('unit_price')); ?>"
                                               <?php else: ?>
                                                   max="0"
                                            <?php endif; ?>
                                        >
                                    </label>
                                </div>
                            </div>
                        </div>
                        <button class="price__filter--btn primary__btn" type="submit">Filter</button>
                    </form>
                </div>
            </div>
        </form>
    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script type="text/javascript">
        function filter(){
            $('#search-form').submit();
        }
        function rangefilter(arg){
            $('input[name=min_price]').val(arg[0]);
            $('input[name=max_price]').val(arg[1]);
            filter();
        }
        function selectShops() {
            // Set the select element to 'shops' and trigger the filter function
            document.getElementsByName('search_by')[0].value = 'shops';
            filter();
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.suruchi.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\CM-ECOM\resources\views/frontend/suruchi/product_listing.blade.php ENDPATH**/ ?>