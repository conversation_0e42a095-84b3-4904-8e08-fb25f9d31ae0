<!DOCTYPE html>
<?php
    $rtl = get_session_language()->rtl;
?>

<html <?php if($rtl == 1): ?> dir="rtl" <?php endif; ?> lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="app-url" content="<?php echo e(getBaseURL()); ?>">
    <meta name="file-base-url" content="<?php echo e(getFileBaseURL()); ?>">

    <title><?php echo $__env->yieldContent('meta_title', get_setting('website_name') . ' | ' . get_setting('site_motto')); ?></title>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="index, follow">
    <meta name="description" content="<?php echo $__env->yieldContent('meta_description', get_setting('meta_description')); ?>" />
    <meta name="keywords" content="<?php echo $__env->yieldContent('meta_keywords', get_setting('meta_keywords')); ?>">

    <?php echo $__env->yieldContent('meta'); ?>

    <?php if(!isset($detailedProduct) && !isset($customer_product) && !isset($shop) && !isset($page) && !isset($blog)): ?>
        <?php
            $meta_image = uploaded_asset(get_setting('meta_image'));
        ?>
            <!-- Schema.org markup for Google+ -->
        <meta itemprop="name" content="<?php echo e(get_setting('meta_title')); ?>">
        <meta itemprop="description" content="<?php echo e(get_setting('meta_description')); ?>">
        <meta itemprop="image" content="<?php echo e($meta_image); ?>">

        <!-- Twitter Card data -->
        <meta name="twitter:card" content="product">
        <meta name="twitter:site" content="@publisher_handle">
        <meta name="twitter:title" content="<?php echo e(get_setting('meta_title')); ?>">
        <meta name="twitter:description" content="<?php echo e(get_setting('meta_description')); ?>">
        <meta name="twitter:creator" content="@author_handle">
        <meta name="twitter:image" content="<?php echo e($meta_image); ?>">

        <!-- Open Graph data -->
        <meta property="og:title" content="<?php echo e(get_setting('meta_title')); ?>" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="<?php echo e(route('home')); ?>" />
        <meta property="og:image" content="<?php echo e($meta_image); ?>" />
        <meta property="og:description" content="<?php echo e(get_setting('meta_description')); ?>" />
        <meta property="og:site_name" content="<?php echo e(env('APP_NAME')); ?>" />
        <meta property="fb:app_id" content="<?php echo e(env('FACEBOOK_PIXEL_ID')); ?>">

    <?php endif; ?>

    <!-- Favicon -->
    <?php
        $site_icon = uploaded_asset(get_setting('site_icon'));
    ?>
    <link rel="icon" href="<?php echo e($site_icon); ?>">
    <link rel="apple-touch-icon" href="<?php echo e($site_icon); ?>">
    <meta name="theme-color" content="#000000">
    <link rel="manifest" href="/manifest.json">


    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo e(static_asset('assets/css/plugins/swiper-bundle.min.css?v=')); ?><?php echo e(rand(1000, 9999)); ?>">
    <link rel="stylesheet" href="<?php echo e(static_asset('assets/css/plugins/glightbox.min.css?v=')); ?><?php echo e(rand(1000, 9999)); ?>">

    <link rel="stylesheet" href="<?php echo e(static_asset('assets/css/vendor/bootstrap.min.css')); ?>">
    <?php if($rtl == 1): ?>
        <link rel="stylesheet" href="<?php echo e(static_asset('assets-minima/css/bootstrap-rtl.min.css')); ?>">
    <?php endif; ?>
    <link rel="stylesheet" href="<?php echo e(static_asset('assets/css/style.css')); ?>">


    <style>
        :root {
            --primary-color: <?php echo e(get_setting('base_color', '#061738')); ?>;
            --primary-hover-color: <?php echo e(get_setting('base_hov_color', '#ee2761')); ?>;
            --secondary-color: <?php echo e(get_setting('secondary_base_color', '#ee2761')); ?>;
            --secondary-hover-color: <?php echo e(get_setting('secondary_base_hov_color', '#061738')); ?>;
        }

        @media screen and (min-width: 540px) {
            .website-popup-modal-dialog {width:600px;}
        }
    </style>

    <script>
        var csrf_token = "<?php echo e(csrf_token()); ?>";
    </script>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        var AIZ = AIZ || {};
        AIZ.local = {
            nothing_selected: '<?php echo translate('Nothing selected', null, true); ?>',
            nothing_found: '<?php echo translate('Nothing found', null, true); ?>',
            choose_file: '<?php echo e(translate('Choose file')); ?>',
            file_selected: '<?php echo e(translate('File selected')); ?>',
            files_selected: '<?php echo e(translate('Files selected')); ?>',
            add_more_files: '<?php echo e(translate('Add more files')); ?>',
            adding_more_files: '<?php echo e(translate('Adding more files')); ?>',
            drop_files_here_paste_or: '<?php echo e(translate('Drop files here, paste or')); ?>',
            browse: '<?php echo e(translate('Browse')); ?>',
            upload_complete: '<?php echo e(translate('Upload complete')); ?>',
            upload_paused: '<?php echo e(translate('Upload paused')); ?>',
            resume_upload: '<?php echo e(translate('Resume upload')); ?>',
            pause_upload: '<?php echo e(translate('Pause upload')); ?>',
            retry_upload: '<?php echo e(translate('Retry upload')); ?>',
            cancel_upload: '<?php echo e(translate('Cancel upload')); ?>',
            uploading: '<?php echo e(translate('Uploading')); ?>',
            processing: '<?php echo e(translate('Processing')); ?>',
            complete: '<?php echo e(translate('Complete')); ?>',
            file: '<?php echo e(translate('File')); ?>',
            files: '<?php echo e(translate('Files')); ?>',
        }
    </script>

    <?php if(get_setting('google_analytics') == 1): ?>
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo e(env('TRACKING_ID')); ?>"></script>

        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '<?php echo e(env('TRACKING_ID')); ?>');
        </script>
    <?php endif; ?>

    <?php if(get_setting('facebook_pixel') == 1): ?>
        <!-- Facebook Pixel Code -->
        <script>
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                n.queue=[];t=b.createElement(e);t.async=!0;
                t.src=v;s=b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t,s)}(window, document,'script',
                'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '<?php echo e(env('FACEBOOK_PIXEL_ID')); ?>');
            fbq('track', 'PageView');
        </script>
        <noscript>
            <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=<?php echo e(env('FACEBOOK_PIXEL_ID')); ?>&ev=PageView&noscript=1"/>
        </noscript>
        <!-- End Facebook Pixel Code -->
    <?php endif; ?>

    <?php
        echo get_setting('header_script');
    ?>
    <link rel="stylesheet" href="<?php echo e(static_asset('assets/vendors/font-awesome/all.min.css')); ?>">
</head>
<body>
<!-- Start preloader -->
<div id="preloader">
    <div id="ctn-preloader" class="ctn-preloader">
        <div class="animation-preloader">
            <div class="spinner"></div>
            <div class="txt-loading">
                    <span data-text-preloader="L" class="letters-loading">
                        L
                    </span>

                <span data-text-preloader="O" class="letters-loading">
                        O
                    </span>

                <span data-text-preloader="A" class="letters-loading">
                        A
                    </span>

                <span data-text-preloader="D" class="letters-loading">
                        D
                    </span>

                <span data-text-preloader="I" class="letters-loading">
                        I
                    </span>

                <span data-text-preloader="N" class="letters-loading">
                        N
                    </span>

                <span data-text-preloader="G" class="letters-loading">
                        G
                    </span>
            </div>
        </div>
        <div class="loader-section section-left"></div>
        <div class="loader-section section-right"></div>
    </div>
</div>
<!-- End preloader -->

<?php
    $tempUserId = session('temp_user_id');
    $user = auth()->user();
    $user_avatar = null;
    $carts = [];
    if ($user && $user->avatar_original != null) {
        $user_avatar = uploaded_asset($user->avatar_original);
    }

    $system_language = get_system_language();

    if ($user != null) {
        $carts = App\Models\Cart::where('user_id', auth()->user()->id)->get();
    }else{
        $carts = App\Models\Cart::where('temp_user_id', $tempUserId)->get();
    }
?>

    <!-- Header -->
<?php echo $__env->make('frontend.suruchi.layouts.partials.nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<!-- aiz-main-wrapper -->
<main class="main__content_wrapper">
    <?php echo $__env->yieldContent('content'); ?>
</main>

<!-- footer -->
<?php echo $__env->make('frontend.suruchi.layouts.partials.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<!-- Floating Buttons -->


<?php if(env("DEMO_MODE") == "On"): ?>
    <!-- demo nav -->
    <?php echo $__env->make('frontend.inc.demo_nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php endif; ?>

<!-- cookies agreement -->
<?php if(get_setting('show_cookies_agreement') == 'on'): ?>
    <div class="aiz-cookie-alert shadow-xl">
        <div class="p-3 bg-dark rounded">
            <div class="text-white mb-3">
                <?php
                    echo get_setting('cookies_agreement_text');
                ?>
            </div>
            <button class="btn btn-primary aiz-cookie-accept">
                <?php echo e(translate('Ok. I Understood')); ?>

            </button>
        </div>
    </div>
<?php endif; ?>

<!-- website popup -->
<?php if(get_setting('show_website_popup') == 'on'): ?>
    <div class="modal" id="showWebsitePopup" data-animation="slideInUp">
        <div class="modal-dialog quickview__main--wrapper website-popup-modal-dialog" style="border-radius: 0px;">
            <header class="modal-header quickview__header">
                <button class="close-modal quickview__close--btn" aria-label="close modal" data-close="">✕ </button>
            </header>
            <div class="c-preloader text-center p-3">
                <i class="las la-spinner la-spin la-3x"></i>
            </div>
            <div class="quickview__inner" id="showWebsitePopup-modal-body">
                <div class="email__popup--content">
                    <div class="rich-text__text rte">
                        <p><?php echo get_setting('website_popup_content'); ?></p>
                    </div>
                    <?php if(get_setting('show_subscribe_form') == 'on'): ?>
                        <div class="email__popup--form">
                            <form method="post" action="<?php echo e(route('subscribers.store')); ?>" id="ContactFooter" accept-charset="UTF-8" class="footer__newsletter newsletter-form" data-cptcha="true" data-hcaptcha-bound="true">
                                <?php echo csrf_field(); ?>
                                <div class="header__search--box" style="width: 100%;margin-top: 20px;">
                                    <label>
                                        <input class="header__search--input" type="email" name="email" class="input__field " value="" aria-required="true" autocorrect="off" autocapitalize="off" autocomplete="email" placeholder="<?php echo e(translate('Your Email Address')); ?>" required=""
                                               style="border-top: 1px solid var(--border-color);border-bottom: 1px solid var(--border-color);">
                                    </label>
                                    <button class="header__search--button bg__secondary text-white" type="submit" aria-label="Subscribe">
                                        <?php echo e(translate('Subscribe Now')); ?>

                                    </button>
                                </div>
                                <div class="h-captcha" data-sitekey="f06e6c50-85a8-45c8-87d0-21a2b65856fe" data-size="invisible">
                                    <iframe aria-hidden="true" data-hcaptcha-widget-id="0c0cdiv6ij28" data-hcaptcha-response="" src="https://newassets.hcaptcha.com/captcha/v1/2766c43/static/hcaptcha.html#frame=checkbox-invisible" style="display: none;"></iframe>
                                    <textarea id="h-captcha-response-0c0cdiv6ij28" name="h-captcha-response" style="display: none;"></textarea>
                                </div>
                            </form>
                        </div>
                </div>
                <?php endif; ?>
                <button type="button" class="bg__secondary text-white mt-5" aria-label="close modal" data-close="">
                    No thanks!
                </button>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php echo $__env->make('frontend.'.get_setting('homepage_select').'.partials.modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php echo $__env->make('frontend.'.get_setting('homepage_select').'.partials.account_delete_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<div class="modal" id="addToCart" data-animation="slideInUp">
    <div class="modal-dialog quickview__main--wrapper" style="height: 600px;">
        <header class="modal-header quickview__header">
            <button class="close-modal quickview__close--btn" aria-label="close modal" data-close="">✕ </button>
        </header>
        <div class="c-preloader text-center p-3">
            <i class="las la-spinner la-spin la-3x"></i>
        </div>
        <div class="quickview__inner" id="addToCart-modal-body">

        </div>
    </div>
</div>

<?php echo $__env->yieldContent('modal'); ?>



<!-- SCRIPTS -->
<script src="<?php echo e(static_asset('assets/js/vendor/popper.js')); ?>"></script>
<script src="<?php echo e(static_asset('assets/js/vendor/bootstrap.min.js')); ?>"></script>
<script src="<?php echo e(static_asset('assets/js/plugins/swiper-bundle.min.js')); ?>"></script>
<script src="<?php echo e(static_asset('assets/js/plugins/glightbox.min.js')); ?>"></script>
<script src="<?php echo e(static_asset('assets/js/script.js?v=')); ?><?php echo e(rand(1000, 9999)); ?>"></script>

<!-- SCRIPTS -->
<script src="<?php echo e(static_asset('assets/js/plugins/jquery-3.7.1.min.js')); ?>"></script>
<script src="<?php echo e(static_asset('assets/js/vendors.js')); ?>"></script>
<script src="<?php echo e(static_asset('assets/js/aiz-core-frontend.js?v=')); ?><?php echo e(rand(1000, 9999)); ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

<?php if(get_setting('facebook_chat') == 1): ?>
    <script type="text/javascript">
        window.fbAsyncInit = function() {
            FB.init({
                xfbml            : true,
                version          : 'v3.3'
            });
        };

        (function(d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) return;
            js = d.createElement(s); js.id = id;
            js.src = 'https://connect.facebook.net/en_US/sdk/xfbml.customerchat.js';
            fjs.parentNode.insertBefore(js, fjs);
        }(document, 'script', 'facebook-jssdk'));
    </script>
    <div id="fb-root"></div>
    <!-- Your customer chat code -->
    <div class="fb-customerchat"
         attribution=setup_tool
         page_id="<?php echo e(env('FACEBOOK_PAGE_ID')); ?>">
    </div>
<?php endif; ?>

<script>
    <?php $__currentLoopData = session('flash_notification', collect())->toArray(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    AIZ.plugins.notify('<?php echo e($message['level']); ?>', '<?php echo e($message['message']); ?>');
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    var canGuestAddToCart = <?php echo e(get_setting('guest_can_add_product_to_cart', 0)); ?>;
</script>

<script>
    <?php if(Route::currentRouteName() == 'home' || Route::currentRouteName() == '/'): ?>

    $.post('<?php echo e(route('home.section.featured')); ?>', {
        _token: '<?php echo e(csrf_token()); ?>'
    }, function(data) {
        $('#section_featured').html(data);
        AIZ.plugins.slickCarousel();
    });

    $.post('<?php echo e(route('home.section.todays_deal')); ?>', {
        _token: '<?php echo e(csrf_token()); ?>'
    }, function(data) {
        $('#todays_deal').html(data);
        AIZ.plugins.slickCarousel();
    });

    $.post('<?php echo e(route('home.section.best_selling')); ?>', {
        _token: '<?php echo e(csrf_token()); ?>'
    }, function(data) {
        $('#section_best_selling').html(data);
        AIZ.plugins.slickCarousel();
    });

    $.post('<?php echo e(route('home.section.newest_products')); ?>', {
        _token: '<?php echo e(csrf_token()); ?>'
    }, function(data) {
        $('#section_newest').html(data);
        AIZ.plugins.slickCarousel();
    });

    $.post('<?php echo e(route('home.section.auction_products')); ?>', {
        _token: '<?php echo e(csrf_token()); ?>'
    }, function(data) {
        $('#auction_products').html(data);
        AIZ.plugins.slickCarousel();
    });

    $.post('<?php echo e(route('home.section.home_categories')); ?>', {
        _token: '<?php echo e(csrf_token()); ?>'
    }, function(data) {
        $('#section_home_categories').html(data);
        AIZ.plugins.slickCarousel();
    });
    <?php endif; ?>

    $(document).ready(function() {
        $('.category-nav-element').each(function(i, el) {

            $(el).on('mouseover', function(){
                if(!$(el).find('.sub-cat-menu').hasClass('loaded')){
                    $.post('<?php echo e(route('category.elements')); ?>', {
                        _token: csrf_token,
                        id:$(el).data('id'
                        )}, function(data){
                        $(el).find('.sub-cat-menu').addClass('loaded').html(data);
                    });
                }
            });
        });

        
        
        
        
        
        
        
        
        

        
        
        

        if ($('#currency-change').length > 0) {
            $('#currency-change .dropdown-menu a').each(function() {
                $(this).on('click', function(e){
                    e.preventDefault();
                    var $this = $(this);
                    var currency_code = $this.data('currency');
                    $.post('<?php echo e(route('currency.change')); ?>',{_token: csrf_token, currency_code:currency_code}, function(data){
                        location.reload();
                    });

                });
            });
        }
    });
    
$(document).ready(function () {
    function updateSelection(name) {
        // Find all radio buttons with the same name
        let group = $('input[name="' + name + '"]');

        // Reset styles for all labels in this group
        group.each(function () {
            $('label[for="' + $(this).attr('id') + '"]').css({
                'background-color': 'white',
                'color': 'black'
            });
        });

        // Apply styles to the selected radio button's label
        let selectedInput = group.filter(':checked');
        $('label[for="' + selectedInput.attr('id') + '"]').css({
            'background-color': 'black',
            'color': 'white'
        });
    }

    // Event listener for radio button change
    $(document).on('change', 'input[type="radio"]', function () {
        updateSelection($(this).attr('name'));
    });

    // Initialize styles on page load
    $('input[type="radio"]:checked').each(function () {
        updateSelection($(this).attr('name'));
    });
});



    $('#search').on('keyup', function(){
        search();
    });

    $('#search').on('focus', function(){
        search();
    });

    function changeLanguage(languageCode) {
        $.post('<?php echo e(route('language.change')); ?>',{_token: csrf_token, locale:languageCode}, function(data){
            location.reload();
        });
    }

    function search(){
        var searchKey = $('#search').val();
        if(searchKey.length > 0){
            $('body').addClass("typed-search-box-shown");

            $('.typed-search-box').removeClass('d-none');
            $('.search-preloader').removeClass('d-none');
            $.post('<?php echo e(route('search.ajax')); ?>', { _token: csrf_token, search:searchKey}, function(data){
                if(data == '0'){
                    // $('.typed-search-box').addClass('d-none');
                    $('#search-content').html(null);
                    $('.typed-search-box .search-nothing').removeClass('d-none').html('<?php echo e(translate('Sorry, nothing found for')); ?> <strong>"'+searchKey+'"</strong>');
                    $('.search-preloader').addClass('d-none');

                }
                else{
                    $('.typed-search-box .search-nothing').addClass('d-none').html(null);
                    $('#search-content').html(data);
                    $('.search-preloader').addClass('d-none');
                }
            });
        }
        else {
            $('.typed-search-box').addClass('d-none');
            $('body').removeClass("typed-search-box-shown");
        }
    }

    $(".aiz-user-top-menu").on("mouseover", function (event) {
        $(".hover-user-top-menu").addClass('active');
    })
        .on("mouseout", function (event) {
            $(".hover-user-top-menu").removeClass('active');
        });

    $(document).on("click", function(event){
        var $trigger = $("#category-menu-bar");
        if($trigger !== event.target && !$trigger.has(event.target).length){
            $("#click-category-menu").slideUp("fast");;
            $("#category-menu-bar-icon").removeClass('show');
        }
    });

    function updateNavCart(view,count){
        $('.cart-count').html(count);
        $('#cart_items').html(view);
    }

    function removeFromCart(key, reloadPage=false){
        $.post('<?php echo e(route('cart.removeFromCart')); ?>', {
            _token  : csrf_token,
            id      :  key
        }, function(data){
            updateNavCart(data.nav_cart_view,data.cart_count);
            $('#cart-summary').html(data.cart_view);
            AIZ.plugins.notify('success', "<?php echo e(translate('Item has been removed from cart')); ?>");
            if(reloadPage){
                window.location.reload();
            }
            $('#cart_items_sidenav').html(parseInt($('#cart_items_sidenav').html())-1);
        });
    }

    function removeFromCartView(e, key) {
        e.preventDefault();
        removeFromCart(key, true);
    }

    function updateQuantity(key, element) {
        $.post('<?php echo e(route('cart.updateQuantity')); ?>', {
            _token: AIZ.data.csrf,
            id: key,
            quantity: element.value
        }, function(data) {
            updateNavCart(data.nav_cart_view, data.cart_count);
            $('#cart-summary').html(data.cart_view);
        });
        AIZ.extra.plusMinus();
    }

    function increaseQuantity(itemId) {
        const input = document.querySelector(`input[name="quantity[${itemId}]"]`);
        const max = parseInt(input.getAttribute("max"));
        let currentValue = parseInt(input.value);

        if (currentValue < max) {
            input.value = currentValue + 1;
            input.dispatchEvent(new Event('change')); // Trigger any change event listeners
        } else {
            alert("Sorry, the maximum limit has been reached");
        }
    }

    function decreaseQuantity(itemId) {
        const input = document.querySelector(`input[name="quantity[${itemId}]"]`);
        const min = parseInt(input.getAttribute("min"));
        let currentValue = parseInt(input.value);

        if (currentValue > min) {
            input.value = currentValue - 1;
            input.dispatchEvent(new Event('change')); // Trigger any change event listeners
        } else {
            alert("Sorry, the minimum limit has been reached");
        }
    }


    function showLoginModal(event) {
        // event.preventDefault(); // Prevent form submission
        $('#login_modal').modal('show').addClass('is-visible');
    }

    function addToCompare(id){
        $.post('<?php echo e(route('compare.addToCompare')); ?>', {_token: csrf_token, id:id}, function(data){
            $('#compare').html(data);
            AIZ.plugins.notify('success', "<?php echo e(translate('Item has been added to compare list')); ?>");
            $('#compare_items_sidenav').html(parseInt($('#compare_items_sidenav').html())+1);
        });
    }

    function addToWishList(id){
        <?php if(Auth::check() && Auth::user()->user_type == 'customer'): ?>
        $.post('<?php echo e(route('wishlists.store')); ?>', {_token: csrf_token, id:id}, function(data){
            if(data != 0){
                $('#wishlist').html(data);
                AIZ.plugins.notify('success', "<?php echo e(translate('Item has been added to wishlist')); ?>");
            }
            else{
                AIZ.plugins.notify('warning', "<?php echo e(translate('Please login first')); ?>");
            }
        });
        <?php elseif(Auth::check() && Auth::user()->user_type != 'customer'): ?>
        AIZ.plugins.notify('warning', "<?php echo e(translate('Please Login as a customer to add products to the WishList.')); ?>");
        <?php else: ?>
        AIZ.plugins.notify('warning', "<?php echo e(translate('Please login first')); ?>");
        <?php endif; ?>
    }

    function showAddToCartModal(id){
        if(!$('#modal-size').hasClass('modal-lg')){
            $('#modal-size').addClass('modal-lg');
        }
        $('#addToCart-modal-body').html(null);
        $('#addToCart').modal('show').addClass('is-visible');
        $('.c-preloader').show();
        $.post('<?php echo e(route('cart.showCartModal')); ?>', {_token: csrf_token, id:id}, function(data){
            $('.c-preloader').hide();
            $('#addToCart-modal-body').html(data);
            AIZ.plugins.slickCarousel();
            AIZ.plugins.zoom();
            AIZ.extra.plusMinus();
            getVariantPrice();
        });
    }

    $('#option-choice-form input').on('change', function(){
        getVariantPrice();
    });

    function getVariantPrice(){
        if($('#option-choice-form input[name=quantity]').val() > 0 && checkAddToCartValidity()){
            $.ajax({
                type:"POST",
                url: '<?php echo e(route('products.variant_price')); ?>',
                data: $('#option-choice-form').serializeArray(),
                success: function(data){
                    $('.product-gallery-thumb .carousel-box').each(function (i) {
                        if($(this).data('variation') && data.variation == $(this).data('variation')){
                            $('.product-gallery-thumb').slick('slickGoTo', i);
                        }
                    })

                    $('#option-choice-form #chosen_price_div').removeClass('d-none');
                    $('#option-choice-form #chosen_price_div #chosen_price').html(data.price);
                    $('#available-quantity').html(data.quantity);
                    $('.input-number').prop('max', data.max_limit);
                    if(parseInt(data.in_stock) == 0 && data.digital  == 0){
                        $('.buy-now').addClass('d-none');
                        $('.add-to-cart').addClass('d-none');
                        $('.out-of-stock').removeClass('d-none');
                    }
                    else{
                        $('.buy-now').removeClass('d-none');
                        $('.add-to-cart').removeClass('d-none');
                        $('.out-of-stock').addClass('d-none');
                    }

                    AIZ.extra.plusMinus();
                }
            });
        }
    }

    function checkAddToCartValidity(){
        var names = {};
        $('#option-choice-form input:radio').each(function() { // find unique names
            names[$(this).attr('name')] = true;
        });
        var count = 0;
        $.each(names, function() { // then count them
            count++;
        });

        if($('#option-choice-form input:radio:checked').length == count){
            return true;
        }

        return false;
    }

    function addToCart(){
        var showCustomerLoginError = false;

        if(!canGuestAddToCart){
            <?php if(Auth::check()): ?>
            <?php if(Auth::user()->user_type != 'customer'): ?>
            var showCustomerLoginError = true;
            <?php endif; ?>
                <?php else: ?>
                showCustomerLoginError = true;
            <?php endif; ?>
        }

        if(showCustomerLoginError){
            Swal.fire({
                icon: "error",
                title: "Warning",
                text: "<?php echo e(translate('Please Login as a customer to add products to the Cart.')); ?>",
            });
            return false;
        }

        if(checkAddToCartValidity()) {
            // Initialize an empty bookingData array
            const bookingData = [];

            // Get the values of start_date, end_date, and number of guests if they exist
            const startDateElement = document.querySelector('input[name="start_date"]');
            const endDateElement = document.querySelector('input[name="end_date"]');
            const noOfGuestsElement = document.querySelector('input[name="no_of_guests"]');

            // Only add each field if the element exists and has a value
            const startDate = startDateElement ? startDateElement.value : null;
            const endDate = endDateElement ? endDateElement.value : null;
            const noOfGuests = noOfGuestsElement ? noOfGuestsElement.value : null;

            // Add data to bookingData if it’s valid
            if (startDate || endDate || noOfGuests) {
                bookingData.push({
                    name: 'notes',
                    value: JSON.stringify({
                        start_date: startDate || "",
                        end_date: endDate || "",
                        no_of_guests: noOfGuests || ""
                    })
                });
            }

            $('#addToCart').modal('show').addClass('is-visible');
            $('.c-preloader').show();

            // Serialize the form data and append bookingData fields
            const formData = $('#option-choice-form').serializeArray().concat(bookingData);

            $('#addToCart').modal('show').addClass('is-visible');
            $('.c-preloader').show();
            $.ajax({
                type:"POST",
                url: '<?php echo e(route('cart.addToCart')); ?>',
                data: formData,
                success: function(data){
                    $('#addToCart-modal-body').html("");
                    $('.c-preloader').hide();
                    $('#modal-size').removeClass('modal-lg');
                    $('#addToCart-modal-body').html(data.modal_view);
                    AIZ.extra.plusMinus();
                    AIZ.plugins.slickCarousel();
                    updateNavCart(data.nav_cart_view,data.cart_count);
                },
                error: function(data){
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: data.message,
                    });
                }
            });
        }
        else{
            Swal.fire({
                icon: "error",
                title: "Warning",
                text: "<?php echo e(translate('Please choose all the options')); ?>",
            });
        }
    }

    function buyNow(){
        <?php if(Auth::check() && Auth::user()->user_type != 'customer'): ?>
        Swal.fire({
            icon: "error",
            title: "Warning",
            text: "<?php echo e(translate('Please Login as a customer to add products to the Cart.')); ?>",
        });
        return false;
        <?php endif; ?>

        if(checkAddToCartValidity()) {
            $('#addToCart-modal-body').html(null);
            $('#addToCart').modal('show').addClass('is-visible');
            $('.c-preloader').show();
            $.ajax({
                type:"POST",
                url: '<?php echo e(route('cart.addToCart')); ?>',
                data: $('#option-choice-form').serializeArray(),
                success: function(data){
                    if(data.status == 1){
                        $('#addToCart-modal-body').html(data.modal_view);
                        updateNavCart(data.nav_cart_view,data.cart_count);
                        window.location.replace("<?php echo e(route('cart')); ?>");
                    }
                    else{
                        $('#addToCart-modal-body').html(null);
                        $('.c-preloader').hide();
                        $('#modal-size').removeClass('modal-lg');
                        $('#addToCart-modal-body').html(data.modal_view);
                    }
                }
            });
        }
        else{
            Swal.fire({
                icon: "error",
                title: "Warning",
                text: "<?php echo e(translate('Please choose all the options')); ?>",
            });
        }
    }

    function bid_single_modal(bid_product_id, min_bid_amount){
        <?php if(Auth::check() && (isCustomer() || isSeller())): ?>
        var min_bid_amount_text = "(<?php echo e(translate('Min Bid Amount: ')); ?>"+min_bid_amount+")";
        $('#min_bid_amount').text(min_bid_amount_text);
        $('#bid_product_id').val(bid_product_id);
        $('#bid_amount').attr('min', min_bid_amount);
        $('#bid_for_product').modal('show');
        <?php elseif(Auth::check() && isAdmin()): ?>
        AIZ.plugins.notify('warning', '<?php echo e(translate('Sorry, Only customers & Sellers can Bid.')); ?>');
        <?php else: ?>
        $('#login_modal').modal('show');
        <?php endif; ?>
    }

    function clickToSlide(btn,id){
        $('#'+id+' .aiz-carousel').find('.'+btn).trigger('click');
        $('#'+id+' .slide-arrow').removeClass('link-disable');
        var arrow = btn=='slick-prev' ? 'arrow-prev' : 'arrow-next';
        if ($('#'+id+' .aiz-carousel').find('.'+btn).hasClass('slick-disabled')) {
            $('#'+id).find('.'+arrow).addClass('link-disable');
        }
    }

    function goToView(params) {
        document.getElementById(params).scrollIntoView({behavior: "smooth", block: "center"});
    }

    function copyCouponCode(code){
        navigator.clipboard.writeText(code);
        AIZ.plugins.notify('success', "<?php echo e(translate('Coupon Code Copied')); ?>");
    }

    $(document).ready(function(){
        $('.cart-animate').animate({margin : 0}, "slow");

        $({deg: 0}).animate({deg: 360}, {
            duration: 2000,
            step: function(now) {
                $('.cart-rotate').css({
                    transform: 'rotate(' + now + 'deg)'
                });
            }
        });

        setTimeout(function(){
            $('.cart-ok').css({ fill: '#d43533' });
        }, 2000);
    });

    // Function to set a cookie
    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }

    // Function to get a cookie by name
    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    // Function to check if a cookie exists
    function checkCookie(name) {
        var cookie = getCookie(name);
        return cookie !== null;
    }

    // On page load
    $(document).ready(function() {
        // Check if the 'popupDisplayed' cookie exists
        if (!checkCookie('popupDisplayed')) {
            // Show the popup only if the cookie does not exist and if we're on the homepage
            if (window.location.pathname === "/") {
                $('#showWebsitePopup').modal('show').addClass('is-visible');

                // Set a cookie to indicate the popup has been displayed (expires in 1 day)
                setCookie('popupDisplayed', 'true', 1);
            }
        }

        // Attach click event listener to the element with aria-label="close modal"
        $('[aria-label="close modal"]').on('click', function() {
            // Set the style attribute of the body tag to an empty string
            $('body').attr('style', '');
        });
    });

    function showProductImage(productId)
    {
        $('.product-thumbnail-'+productId).hide();
        $('.product-image-'+productId).show();
    }

    function showProductThumnail(productId)
    {
        $('.product-thumbnail-'+productId).show();
        $('.product-image-'+productId).hide();
    }

    function addToCartEvent(productId) {
        var canGuestAddToCart = <?php echo e(get_setting('guest_can_add_product_to_cart', 0)); ?>;
        // Replace the next line with your actual authentication check
        var isAuthenticated = <?php echo e(Auth::check() ? 'true' : 'false'); ?>;

        if (isAuthenticated) {
            // User is authenticated, show the Add to Cart modal
            showAddToCartModal(productId);
        } else if (canGuestAddToCart == 1) {
            showAddToCartModal(productId);
        } else {
            // User is not authenticated and cannot add to cart
            showLoginModal();
        }
    }

    function loginWithEmailOtp() {
        const email = document.getElementById('email').value;
        const emailOtp = document.getElementById('email_otp').value;

        // If OTP field is not empty, submit the form
        if (emailOtp.trim() !== '') {
            const form = document.querySelector('.account__login--inner');
            form.submit();
        } else {
            // Otherwise, send the OTP
            $.ajax({
                url: "<?php echo e(route('user.login.send-otp')); ?>",
                type: "POST",
                data: {
                    _token: "<?php echo e(csrf_token()); ?>",
                    email: email
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#email-input-group').hide();
                        $('#otp-input-group').removeClass('d-none');
                        $('#login-button').text("<?php echo e(translate('Login')); ?>"); // Change button text
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert("Error sending OTP. Please try again.");
                }
            });
        }
    }
</script>

<?php if(addon_is_activated('otp_system')): ?>
    <script type="text/javascript">
        // Country Code
        var isPhoneShown = true,
            countryData = window.intlTelInputGlobals.getCountryData(),
            input = document.querySelector("#phone-code");

        for (var i = 0; i < countryData.length; i++) {
            var country = countryData[i];
            if (country.iso2 == 'bd') {
                country.dialCode = '88';
            }
        }

        var iti = intlTelInput(input, {
            separateDialCode: true,
            utilsScript: "<?php echo e(static_asset('assets/js/intlTelutils.js')); ?>?1590403638580",
            onlyCountries: <?php echo get_active_countries()->pluck('code') ?>,
            customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                if (selectedCountryData.iso2 == 'bd') {
                    return "01xxxxxxxxx";
                }
                return selectedCountryPlaceholder;
            }
        });

        var country = iti.getSelectedCountryData();
        $('input[name=country_code]').val(country.dialCode);

        input.addEventListener("countrychange", function(e) {
            // var currentMask = e.currentTarget.placeholder;
            var country = iti.getSelectedCountryData();
            $('input[name=country_code]').val(country.dialCode);

        });

        function toggleEmailPhone(el) {
            if (isPhoneShown) {
                $('.phone-form-group').addClass('d-none');
                $('.email-form-group').removeClass('d-none');
                $('input[name=phone]').val(null);
                isPhoneShown = false;
                $(el).html('*<?php echo e(translate('Use Phone Number Instead')); ?>');
            } else {
                $('.phone-form-group').removeClass('d-none');
                $('.email-form-group').addClass('d-none');
                $('input[name=email]').val(null);
                isPhoneShown = true;
                $(el).html('<i>*<?php echo e(translate('Use Email Instead')); ?></i>');
            }
        }
    </script>
<?php endif; ?>

<script>
    var acc = document.getElementsByClassName("aiz-accordion-heading");
    var i;
    for (i = 0; i < acc.length; i++) {
        acc[i].addEventListener("click", function() {
            this.classList.toggle("active");
            var panel = this.nextElementSibling;
            if (panel.style.maxHeight) {
                panel.style.maxHeight = null;
            } else {
                panel.style.maxHeight = panel.scrollHeight + "px";
            }
        });
    }
</script>


<script>
    function showFloatingButtons() {
        document.querySelector('.floating-buttons-section').classList.toggle('show');;
    }
</script>

<?php if(env("DEMO_MODE") == "On"): ?>
    <script>
        var demoNav = document.querySelector('.aiz-demo-nav');
        var menuBtn = document.querySelector('.aiz-demo-nav-toggler');
        var lineOne = document.querySelector('.aiz-demo-nav-toggler .aiz-demo-nav-btn .line--1');
        var lineTwo = document.querySelector('.aiz-demo-nav-toggler .aiz-demo-nav-btn .line--2');
        var lineThree = document.querySelector('.aiz-demo-nav-toggler .aiz-demo-nav-btn .line--3');
        menuBtn.addEventListener('click', () => {
            toggleDemoNav();
        });

        function toggleDemoNav() {
            // demoNav.classList.toggle('show');
            demoNav.classList.toggle('shadow-none');
            lineOne.classList.toggle('line-cross');
            lineTwo.classList.toggle('line-fade-out');
            lineThree.classList.toggle('line-cross');
            if ($('.aiz-demo-nav-toggler').hasClass('show')) {
                $('.aiz-demo-nav-toggler').removeClass('show');
                demoHideOverlay();
            }else{
                $('.aiz-demo-nav-toggler').addClass('show');
                demoShowOverlay();
            }
        }

        $('.aiz-demos').click(function(e){
            if (!e.target.closest('.aiz-demos .aiz-demo-content')) {
                toggleDemoNav();
            }
        });

        function demoShowOverlay(){
            $('.top-banner').removeClass('z-1035').addClass('z-1');
            $('.top-navbar').removeClass('z-1035').addClass('z-1');
            $('header').removeClass('z-1020').addClass('z-1');
            $('.aiz-demos').addClass('show');
        }

        function demoHideOverlay(cls=null){
            if($('.aiz-demos').hasClass('show')){
                $('.aiz-demos').removeClass('show');
                $('.top-banner').delay(800).removeClass('z-1').addClass('z-1035');
                $('.top-navbar').delay(800).removeClass('z-1').addClass('z-1035');
                $('header').delay(800).removeClass('z-1').addClass('z-1020');
            }
        }
    </script>
<?php endif; ?>

<script>
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('./serviceworker.js')
            .then((registration) => {
                console.log('Service Worker registered with scope:', registration.scope);
            })
    }
</script>


<?php echo $__env->yieldContent('script'); ?>

<?php
    echo get_setting('footer_script');
?>


<script src="/sw.js">

</script>
<script>
    if('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/sw.js')
                .then((reg) => console.log('Success: ', reg.scope))
                .catch((err) => console.log('Failure: ', err));
        })
    }
</script>

<style>

    .float,
    .float:hover{
        position:fixed;
        width:60px;
        height:60px;
        bottom:40px;
        right:40px;
        background-color:#25d366;
        color:#FFF;
        border-radius:50px;
        text-align:center;
        font-size:30px;
        box-shadow: 2px 2px 3px #999;
        z-index:100;
    }

    .my-float{
        margin-top:16px;
    }

</style>

<?php if(!empty(get_setting('whatsapp_phone_number'))): ?>
    <a href="https://api.whatsapp.com/send?phone=<?php echo e(get_setting('whatsapp_phone_number')); ?>&text=Hello" class="float" >
        <i class="fa fa-whatsapp my-float"></i>
    </a>
<?php endif; ?>

<style>

    .float-phone,
    .float-phone:hover{
        position:fixed;
        width:60px;
        height:60px;
        bottom:40px;
        left:40px;
        background-color:#1597E5;
        color:#FFF;
        border-radius:50px;
        text-align:center;
        font-size:30px;
        box-shadow: 2px 2px 3px #999;
        z-index:100;
    }

    .my-float-phone{
        margin-top:16px;
    }
</style>

<?php if(!empty(get_setting('tel_phone_number'))): ?>
<a href="tel:<?php echo e(get_setting('tel_phone_number')); ?>" class="float-phone" >
    <i class="fa fa-phone my-float-phone"></i>
</a>
<?php endif; ?>


<style>
    @media only screen and (max-width: 600px) {
        .float,
        .float-phone,
        .float:hover,
        .float-phone:hover

        {
            bottom: 82px;
            scale: 0.8;
        }
    }
</style>

</body>
</html>
<?php /**PATH C:\laragon\www\CM-ECOM\resources\views/frontend/suruchi/layouts/app.blade.php ENDPATH**/ ?>