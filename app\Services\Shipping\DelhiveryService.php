<?php

namespace App\Services\Shipping;

use App\Models\Order;
use App\Models\ShippingPartner;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DelhiveryService extends AbstractShippingService
{
    protected $baseUrl = 'https://track.delhivery.com/api';

    public function __construct(ShippingPartner $shippingPartner = null)
    {
        if (!$shippingPartner) {
            $shippingPartner = ShippingPartner::where('code', 'delhivery')->first();
        }

        parent::__construct($shippingPartner);
    }

    /**
     * Calculate shipping rate for an order
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return float
     */
    public function calculateRate(Order $order, array $fromAddress, array $toAddress): float
    {
        $weight = 0;

        // Calculate total weight
        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight += ($product->weight ?? 0.5) * $orderDetail->quantity;
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Token ' . $this->apiKey,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/kinko/v1/invoice/charges/.json', [
                'md' => 'S', // Mode: Surface
                'ss' => 'Delivered', // Service type
                'o_pin' => $fromAddress['postal_code'],
                'd_pin' => $toAddress['postal_code'],
                'cgm' => $weight * 1000, // Weight in grams
                'pt' => $order->payment_type == 'cash_on_delivery' ? 'COD' : 'Pre-paid',
                'cod' => $order->payment_type == 'cash_on_delivery' ? $order->grand_total : 0
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data[0]['total_amount'])) {
                    return (float) $data[0]['total_amount'];
                }
            }
        } catch (\Exception $e) {
            Log::error('Delhivery Rate Calculation Error: ' . $e->getMessage());
        }

        return 120.00; // Fallback rate
    }

    /**
     * Create a shipping order with Delhivery
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return array
     */
    public function createShippingOrder(Order $order, array $fromAddress, array $toAddress): array
    {
        $weight = 0;

        // Calculate total weight
        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight += ($product->weight ?? 0.5) * $orderDetail->quantity;
        }

        $shipmentData = [
            'name' => $order->user->name,
            'add' => $toAddress['address'],
            'pin' => $toAddress['postal_code'],
            'city' => $toAddress['city'],
            'state' => $toAddress['state'],
            'country' => $toAddress['country'],
            'phone' => $toAddress['phone'],
            'order' => $order->code,
            'payment_mode' => $order->payment_type == 'cash_on_delivery' ? 'COD' : 'Prepaid',
            'return_pin' => $fromAddress['postal_code'],
            'return_city' => $fromAddress['city'],
            'return_phone' => $fromAddress['phone'],
            'return_add' => $fromAddress['address'],
            'return_state' => $fromAddress['state'],
            'return_country' => $fromAddress['country'],
            'products_desc' => 'E-commerce Products',
            'hsn_code' => '',
            'cod_amount' => $order->payment_type == 'cash_on_delivery' ? $order->grand_total : 0,
            'order_date' => date('Y-m-d H:i:s', $order->date),
            'total_amount' => $order->grand_total,
            'seller_add' => $fromAddress['address'],
            'seller_name' => $fromAddress['name'],
            'seller_inv' => $order->code,
            'quantity' => $order->orderDetails->sum('quantity'),
            'waybill' => '',
            'shipment_width' => 10,
            'shipment_height' => 5,
            'weight' => $weight,
            'seller_gst_tin' => '',
            'shipping_mode' => 'Surface',
            'address_type' => 'home'
        ];

        $data = [
            'format' => 'json',
            'data' => json_encode([
                'shipments' => [$shipmentData]
            ])
        ];

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Token ' . $this->apiKey,
                'Content-Type' => 'application/json'
            ])->post($this->baseUrl . '/cmu/create.json', $data);

            if ($response->successful()) {
                $responseData = $response->json();

                if (isset($responseData['packages']) && !empty($responseData['packages'])) {
                    $package = $responseData['packages'][0];

                    return [
                        'success' => true,
                        'tracking_id' => $package['waybill'] ?? null,
                        'shipment_id' => $package['refnum'] ?? null,
                        'response' => json_encode($responseData)
                    ];
                }
            }

            Log::error('Delhivery Order Creation Failed: ' . $response->body());
            return [
                'success' => false,
                'message' => 'Failed to create order with Delhivery: ' . $response->body()
            ];

        } catch (\Exception $e) {
            Log::error('Delhivery Order Creation Error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error creating order with Delhivery: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Track a shipment with Delhivery
     *
     * @param string $trackingId
     * @return array
     */
    public function trackShipment(string $trackingId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Token ' . $this->apiKey,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/v1/packages/json/', [
                'waybill' => $trackingId
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['ShipmentData']) && !empty($data['ShipmentData'])) {
                    $shipmentData = $data['ShipmentData'][0]['Shipment'];
                    $status = $shipmentData['Status'];

                    return [
                        'tracking_id' => $trackingId,
                        'status' => $status['Status'] ?? 'unknown',
                        'current_location' => $status['Instructions'] ?? 'Unknown',
                        'expected_delivery' => $shipmentData['ExpectedDeliveryDate'] ?? null,
                        'tracking_url' => 'https://www.delhivery.com/track/package/' . $trackingId,
                        'tracking_data' => $shipmentData
                    ];
                }
            }

            return [
                'tracking_id' => $trackingId,
                'status' => 'unknown',
                'message' => 'No tracking data found'
            ];

        } catch (\Exception $e) {
            Log::error('Delhivery Tracking Error: ' . $e->getMessage());
            return [
                'tracking_id' => $trackingId,
                'status' => 'error',
                'message' => 'Error tracking shipment: ' . $e->getMessage()
            ];
        }
    }
}
