<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;

class AddShippingPartnerPermissions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add permission for managing shipping partners
        Permission::create([
            'name' => 'manage_shipping_partners',
            'section' => 'shipping',
            'guard_name' => 'web',
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove permission for managing shipping partners
        Permission::where('name', 'manage_shipping_partners')->delete();
    }
}
