


<?php $__env->startSection('content'); ?>

    <div class="login__section section--padding">
        <div class="container">
            <form class="form-default account__login--inner" role="form" action="<?php echo e(route('login')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="login__section--inner">
                    <div class="row">
                        <div class="col-md-3 col-lg-3 col-sm-0"></div>
                        <div class="col-md-6 col-lg-6">
                            <div class="account__login">
                                <div class="account__login--header mb-25">
                                    <h2 class="account__login--header__title h3 mb-10"><?php echo e(translate('Welcome Back!')); ?></h2>
                                    <p class="account__login--header__desc">Login if you are a returning customer.</p>
                                </div>
                                <div class="account__login--inner">
                                    <!-- Conditional Email/Phone Login -->
                                    <?php if(get_setting('otp_system')): ?>
                                        <div class="form-group phone-form-group mb-1">
                                            <input type="tel" id="phone-code" class="account__login--input" name="phone" placeholder="<?php echo e(translate('Phone')); ?>" value="<?php echo e(old('phone')); ?>" autocomplete="off">
                                        </div>
                                        <input type="hidden" name="country_code" value="+91">
                                        <div class="form-group email-form-group mb-1 d-none">
                                            <input type="email" class="account__login--input" name="email" placeholder="<?php echo e(translate('Email')); ?>" value="<?php echo e(old('email')); ?>" autocomplete="off">
                                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback" role="alert"><strong><?php echo e($message); ?></strong></span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <button type="button" class="btn btn-link p-0 text-primary fs-12" onclick="toggleEmailPhone(this)">
                                            <i>*<?php echo e(translate('Use Email Instead')); ?></i>
                                        </button>
                                    <?php else: ?>
                                        <div class="form-group">
                                            <input type="email" class="account__login--input" placeholder="<?php echo e(translate('Email')); ?>" name="email" value="<?php echo e(old('email')); ?>" autocomplete="off">
                                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback" role="alert"><strong><?php echo e($message); ?></strong></span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Phone OTP Field (for phone login) -->
                                    <?php if(get_setting('otp_system')): ?>
                                        <div id="phone-otp-input-group" class="form-group d-none">
                                            <input type="text" id="phone_otp" name="phone_otp" class="account__login--input" placeholder="<?php echo e(translate('Enter Phone OTP')); ?>" autocomplete="off">
                                        </div>
                                    <?php endif; ?>

                                    <!-- Email OTP Field (for email OTP login) -->
                                    <?php if(get_setting('ask_email_otp_to_login')): ?>
                                        <div id="email-otp-input-group" class="form-group d-none">
                                            <input type="text" id="email_otp" name="email_otp" class="account__login--input" placeholder="<?php echo e(translate('Enter Email OTP')); ?>" autocomplete="off">
                                        </div>
                                    <?php endif; ?>

                                    <!-- Password Field (for regular login) -->
                                    <?php if(!get_setting('ask_email_otp_to_login')): ?>
                                        <div id="password-input-group" class="form-group">
                                            <label for="password" class="fs-12 fw-700 text-soft-dark"></label>
                                            <div class="position-relative">
                                                <input type="password" id="password" class="account__login--input" name="password" placeholder="<?php echo e(translate('Password')); ?>">
                                                <i class="password-toggle las la-eye"></i>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="account__login--remember__forgot mb-15 d-flex justify-content-between align-items-center">
                                        <div class="account__login--remember position__relative">
                                            <input class="checkout__checkbox--input" id="check1" type="checkbox" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                                            <span class="checkout__checkbox--checkmark"></span>
                                            <label class="checkout__checkbox--label login__remember--label" for="check1">
                                                Remember me</label>
                                        </div>
                                        <a href="<?php echo e(route('password.request')); ?>" class="account__login--forgot">Forgot Your Password?</a>
                                    </div>

                                    <!-- Dynamic Login Button -->
                                    <button id="login-btn" class="account__login--btn primary__btn" type="button" onclick="handleLogin()">
                                        <?php echo e(translate('Login')); ?>

                                    </button>

                                    <!-- Social Login -->
                                    <?php if(get_setting('google_login') || get_setting('facebook_login') || get_setting('twitter_login') || get_setting('apple_login')): ?>
                                        <div class="account__login--divide"><span class="account__login--divide__text">OR</span></div>
                                        <div class="account__social d-flex justify-content-center mb-15">
                                            <?php if(get_setting('facebook_login')): ?>
                                                <a href="<?php echo e(route('social.login', ['provider' => 'facebook'])); ?>" class="account__social--link facebook">Facebook</a>
                                            <?php endif; ?>
                                            <?php if(get_setting('google_login')): ?>
                                                <a href="<?php echo e(route('social.login', ['provider' => 'google'])); ?>" class="account__social--link google">Google</a>
                                            <?php endif; ?>
                                            <?php if(get_setting('twitter_login')): ?>
                                                <a href="<?php echo e(route('social.login', ['provider' => 'twitter'])); ?>" class="account__social--link twitter">Twitter</a>
                                            <?php endif; ?>
                                            <?php if(get_setting('apple_login')): ?>
                                                <a href="<?php echo e(route('social.login', ['provider' => 'apple'])); ?>" class="account__social--link apple">Apple</a>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Register Now and Back Links -->
                                    <p class="account__login--signup__text"><?php echo e(translate("Don't have an account?")); ?>

                                        <a href="<?php echo e(route('user.registration')); ?>">Register now</a>
                                    </p>
                                    <a href="<?php echo e(url()->previous()); ?>" class="mt-3 fs-14 fw-700 text-primary">
                                        <i class="las la-arrow-left fs-20 mr-1"></i><?php echo e(translate('Back to Previous Page')); ?>

                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script type="text/javascript">
        function autoFillCustomer(){
            $('#email').val('<EMAIL>');
            $('#password').val('123456');
        }

        // Toggle between email and phone input
        function toggleEmailPhone(button) {
            const phoneGroup = $('.phone-form-group');
            const emailGroup = $('.email-form-group');
            const phoneOtpGroup = $('#phone-otp-input-group');

            if (phoneGroup.hasClass('d-none')) {
                // Switch to phone
                phoneGroup.removeClass('d-none');
                emailGroup.addClass('d-none');
                phoneOtpGroup.addClass('d-none');
                $(button).html('<i>*<?php echo e(translate("Use Email Instead")); ?></i>');
                $('#login-btn').text('<?php echo e(translate("Send OTP")); ?>');
            } else {
                // Switch to email
                phoneGroup.addClass('d-none');
                emailGroup.removeClass('d-none');
                phoneOtpGroup.addClass('d-none');
                $(button).html('<i>*<?php echo e(translate("Use Phone Instead")); ?></i>');
                updateLoginButtonText();
            }
        }

        // Update login button text based on current mode
        function updateLoginButtonText() {
            <?php if(get_setting('ask_email_otp_to_login')): ?>
                $('#login-btn').text('<?php echo e(translate("Send OTP")); ?>');
            <?php else: ?>
                $('#login-btn').text('<?php echo e(translate("Login")); ?>');
            <?php endif; ?>
        }

        // Unified login handler
        function handleLogin() {
            const isPhoneVisible = !$('.phone-form-group').hasClass('d-none');
            const isEmailVisible = !$('.email-form-group').hasClass('d-none');

            if (isPhoneVisible) {
                handlePhoneLogin();
            } else if (isEmailVisible) {
                <?php if(get_setting('ask_email_otp_to_login')): ?>
                    handleEmailOtpLogin();
                <?php else: ?>
                    handleRegularLogin();
                <?php endif; ?>
            } else {
                handleRegularLogin();
            }
        }

        // Handle phone OTP login
        function handlePhoneLogin() {
            const phone = $('#phone-code').val();
            const phoneOtp = $('#phone_otp').val();

            if (phoneOtp.trim() !== '') {
                // Submit form with phone OTP
                submitLoginForm();
            } else {
                // Send phone OTP
                if (!phone.trim()) {
                    alert('<?php echo e(translate("Please enter phone number")); ?>');
                    return;
                }

                $.ajax({
                    url: "<?php echo e(route('user.login.send-otp')); ?>",
                    type: "POST",
                    data: {
                        _token: "<?php echo e(csrf_token()); ?>",
                        phone: phone,
                        country_code: $('input[name="country_code"]').val()
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#phone-otp-input-group').removeClass('d-none');
                            $('#login-btn').text('<?php echo e(translate("Login")); ?>');
                            alert('<?php echo e(translate("OTP sent to your phone")); ?>');
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function() {
                        alert('<?php echo e(translate("Error sending OTP. Please try again.")); ?>');
                    }
                });
            }
        }

        // Handle email OTP login
        function handleEmailOtpLogin() {
            const email = $('input[name="email"]').val();
            const emailOtp = $('#email_otp').val();

            if (emailOtp.trim() !== '') {
                // Submit form with email OTP
                submitLoginForm();
            } else {
                // Send email OTP
                if (!email.trim()) {
                    alert('<?php echo e(translate("Please enter email address")); ?>');
                    return;
                }

                $.ajax({
                    url: "<?php echo e(route('user.login.send-otp')); ?>",
                    type: "POST",
                    data: {
                        _token: "<?php echo e(csrf_token()); ?>",
                        email: email
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#email-otp-input-group').removeClass('d-none');
                            $('#login-btn').text('<?php echo e(translate("Login")); ?>');
                            alert('<?php echo e(translate("OTP sent to your email")); ?>');
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function() {
                        alert('<?php echo e(translate("Error sending OTP. Please try again.")); ?>');
                    }
                });
            }
        }

        // Handle regular email/password login
        function handleRegularLogin() {
            const email = $('input[name="email"]').val();
            const password = $('#password').val();

            if (!email.trim()) {
                alert('<?php echo e(translate("Please enter email address")); ?>');
                return;
            }

            if (!password.trim()) {
                alert('<?php echo e(translate("Please enter password")); ?>');
                return;
            }

            submitLoginForm();
        }

        // Submit the login form
        function submitLoginForm() {
            const form = $('.form-default')[0];
            form.action = "<?php echo e(route('cart.login.submit')); ?>";
            form.submit();
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.suruchi.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\CM-ECOM\resources\views/auth/suruchi/user_login.blade.php ENDPATH**/ ?>