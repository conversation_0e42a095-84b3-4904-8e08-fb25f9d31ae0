<?php

namespace App\Http\Controllers;

use App\Models\City;
use App\Models\Seller;
use Illuminate\Http\Request;
use App\Models\Search;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Color;
use App\Models\Shop;
use App\Models\Attribute;
use App\Models\AttributeCategory;
use App\Utility\CategoryUtility;

class SearchController extends Controller
{
    public function index(Request $request, $category_id = null, $brand_id = null)
    {
        $searchType = $request->search_by;
        $query = $request->keyword;
        $sort_by = $request->sort_by;
        $min_price = $request->min_price;
        $max_price = $request->max_price;
        $seller_id = $request->seller_id;
        $city_id = $request->location;
        $latitude = $request->latitude;
        $longitude = $request->longitude;
        $attributes = Attribute::all();
        $selected_attribute_values = array();
        $colors = Color::all();
        $selected_color = null;
        $category = [];
        $categories = [];
        $radiusFromSearchLocation = 50;

        $conditions = [];

        if ($brand_id != null) {
            $conditions = array_merge($conditions, ['products.brand_id' => $brand_id]);
        } elseif ($request->brand != null) {
            $brand_id = (Brand::where('slug', $request->brand)->first() != null) ? Brand::where('slug', $request->brand)->first()->id : null;
            $conditions = array_merge($conditions, ['products.brand_id' => $brand_id]);
        }

         if ($seller_id != null) {
             $conditions = array_merge($conditions, ['products.user_id' => Seller::findOrFail($seller_id)->user->id]);
         }

         if(!empty($searchType) && strcasecmp($searchType, 'shops') === 0){
            if(get_setting('enable_search_with_location') == 1){
                if ($city_id === 'current' && $latitude && $longitude) {
                    // Use Haversine formula for proximity search
                    $distanceQuery = "(6371 * acos(cos(radians($latitude)) * cos(radians(latitude)) * cos(radians(longitude) - radians($longitude)) + sin(radians($latitude)) * sin(radians(latitude))))";
                    $products = Shop::select('*')
                        ->selectRaw("$distanceQuery AS distance")
                        ->having('distance', '<=', $radiusFromSearchLocation) // 50 km radius, adjust as needed
                        ->orderBy('distance');
                } else {
                    // Fallback to city_id-based filtering
                    $conditions = array_merge($conditions, ['city_id' => $city_id]);
                    $products = Shop::where($conditions);
                }
            }else{
               $products = Shop::where($conditions);
            }

             if ($query != null) {
                 $searchController = new SearchController;
                 $searchController->store($request);

                 $products->where(function ($q) use ($query) {
                     foreach (explode(' ', trim($query)) as $word) {
                         $q->where('name', 'like', '%' . $word . '%');
                     }
                 });

                 $case1 = $query . '%';
                 $case2 = '%' . $query . '%';

                 $products->orderByRaw("CASE
                WHEN name LIKE '$case1' THEN 1
                WHEN name LIKE '$case2' THEN 2
                ELSE 3
                END");
             }
             $products = $products->paginate(24)->appends(request()->query());

             return view('frontend.'.get_setting('homepage_select').'.shops_listing', compact('products', 'query', 'category', 'categories', 'category_id', 'brand_id', 'sort_by', 'seller_id', 'min_price', 'max_price', 'attributes', 'selected_attribute_values', 'colors', 'selected_color'));
         }else{
            if(get_setting('enable_search_with_location') == 1){
                if ($city_id === 'current' && $latitude && $longitude) {
                    // Use Haversine formula for proximity search
                    $distanceQuery = "(6371 * acos(cos(radians($latitude)) * cos(radians(shops.latitude)) * cos(radians(shops.longitude) - radians($longitude)) + sin(radians($latitude)) * sin(radians(shops.latitude))))";
                    $products = Product::where($conditions)
                        ->join('users', 'products.user_id', '=', 'users.id')
                        ->join('shops', 'users.id', '=', 'shops.user_id')
                        ->selectRaw("products.*, $distanceQuery AS distance")
                        ->whereRaw("$distanceQuery <= ?", [$radiusFromSearchLocation]) // 50 km radius
                        ->orderBy('distance');
                } else {
                    // Fallback to city_id-based filtering
                    $conditions = array_merge($conditions, ['city_id' => $city_id]);
                    $products = Product::where($conditions)
                        ->join('users', 'products.user_id', '=', 'users.id')
                        ->join('shops', 'users.id', '=', 'shops.user_id')
                        ->where('shops.city_id', $city_id);
                }
            }else{
                $products = Product::where($conditions);
            }

             if ($category_id != null) {
                 $category_ids = CategoryUtility::children_ids($category_id);
                 $category_ids[] = $category_id;
                 $category = Category::with('childrenCategories')->find($category_id);

                 $products = $category->products();

                 $attribute_ids = AttributeCategory::whereIn('category_id', $category_ids)->pluck('attribute_id')->toArray();
                 $attributes = Attribute::whereIn('id', $attribute_ids)->get();
             } else {
                 $categories = Category::with('childrenCategories', 'coverImage')->where('level', 0)->orderBy('order_level', 'desc')->get();
             }

             if ($min_price != null && $max_price != null) {
                 $products->where('products.unit_price', '>=', $min_price)->where('products.unit_price', '<=', $max_price);
             }

             if ($query != null) {
                 $searchController = new SearchController;
                 $searchController->store($request);

                 $products->where(function ($q) use ($query) {
                     foreach (explode(' ', trim($query)) as $word) {
                         $q->where('products.name', 'like', '%' . $word . '%')
                             ->orWhere('products.tags', 'like', '%' . $word . '%')
                             ->orWhereHas('product_translations', function ($q) use ($word) {
                                 $q->where('name', 'like', '%' . $word . '%');
                             })
                             ->orWhereHas('stocks', function ($q) use ($word) {
                                 $q->where('sku', 'like', '%' . $word . '%');
                             });
                     }
                 });

                 $case1 = $query . '%';
                 $case2 = '%' . $query . '%';

                 $products->orderByRaw("CASE
                WHEN products.name LIKE '$case1' THEN 1
                WHEN products.name LIKE '$case2' THEN 2
                ELSE 3
                END");
             }

             switch ($sort_by) {
                 case 'newest':
                     $products->orderBy('products.created_at', 'desc');
                     break;
                 case 'oldest':
                     $products->orderBy('products.created_at', 'asc');
                     break;
                 case 'price-asc':
                     $products->orderBy('products.unit_price', 'asc');
                     break;
                 case 'price-desc':
                     $products->orderBy('products.unit_price', 'desc');
                     break;
                 default:
                     $products->orderBy('products.id', 'desc');
                     break;
             }

             if ($request->has('color')) {
                 $str = '"' . $request->color . '"';
                 $products->where('products.colors', 'like', '%' . $str . '%');
                 $selected_color = $request->color;
             }

             if ($request->has('selected_attribute_values')) {
                 $selected_attribute_values = $request->selected_attribute_values;
                 $products->where(function ($query) use ($selected_attribute_values) {
                     foreach ($selected_attribute_values as $key => $value) {
                         $str = '"' . $value . '"';

                         $query->orWhere('products.choice_options', 'like', '%' . $str . '%');
                     }
                 });
             }

             $products = filter_search_products($products)->with('taxes')->paginate(24)->appends(request()->query());

             return view('frontend.'.get_setting('homepage_select').'.product_listing', compact('products', 'query', 'category', 'categories', 'category_id', 'brand_id', 'sort_by', 'seller_id', 'min_price', 'max_price', 'attributes', 'selected_attribute_values', 'colors', 'selected_color'));
         }
    }

    public function listing(Request $request)
    {
        return $this->index($request);
    }

    public function listingByCategory(Request $request, $category_slug)
    {
        $category = Category::where('slug', $category_slug)->first();
        if ($category != null) {
            return $this->index($request, $category->id);
        }
        abort(404);
    }

    public function listingByBrand(Request $request, $brand_slug)
    {
        $brand = Brand::where('slug', $brand_slug)->first();
        if ($brand != null) {
            return $this->index($request, null, $brand->id);
        }
        abort(404);
    }

    //Suggestional Search
    public function ajax_search(Request $request)
    {
        $keywords = array();
        $query = $request->search;
        $products = Product::where('published', 1)->where('tags', 'like', '%' . $query . '%')->get();
        foreach ($products as $key => $product) {
            foreach (explode(',', $product->tags) as $key => $tag) {
                if (stripos($tag, $query) !== false) {
                    if (sizeof($keywords) > 5) {
                        break;
                    } else {
                        if (!in_array(strtolower($tag), $keywords)) {
                            array_push($keywords, strtolower($tag));
                        }
                    }
                }
            }
        }

        $products_query = filter_products(Product::query());

        $products_query = $products_query->where('published', 1)
            ->where(function ($q) use ($query) {
                foreach (explode(' ', trim($query)) as $word) {
                    $q->where('name', 'like', '%' . $word . '%')
                        ->orWhere('tags', 'like', '%' . $word . '%')
                        ->orWhereHas('product_translations', function ($q) use ($word) {
                            $q->where('name', 'like', '%' . $word . '%');
                        })
                        ->orWhereHas('stocks', function ($q) use ($word) {
                            $q->where('sku', 'like', '%' . $word . '%');
                        });
                }
            });
        $case1 = $query . '%';
        $case2 = '%' . $query . '%';

        $products_query->orderByRaw("CASE
                WHEN name LIKE '$case1' THEN 1
                WHEN name LIKE '$case2' THEN 2
                ELSE 3
                END");
        $products = $products_query->limit(3)->get();

        $categories = Category::where('name', 'like', '%' . $query . '%')->get()->take(3);

        $shops = Shop::whereIn('user_id', verified_sellers_id())->where('name', 'like', '%' . $query . '%')->get()->take(3);

        if (sizeof($keywords) > 0 || sizeof($categories) > 0 || sizeof($products) > 0 || sizeof($shops) > 0) {
            return view('frontend.'.get_setting('homepage_select').'.partials.search_content', compact('products', 'categories', 'keywords', 'shops'));
        }
        return '0';
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $search = Search::where('query', $request->keyword)->first();
        if ($search != null) {
            $search->count = $search->count + 1;
            $search->save();
        } else {
            $search = new Search;
            $search->query = $request->keyword;
            $search->save();
        }
    }

    public function getNearestCity(Request $request)
    {
        $latitude = $request->latitude;
        $longitude = $request->longitude;

        // Example logic to find the nearest city using Haversine formula or similar
        $city = City::select('id', 'name')
            ->selectRaw("(
            6371 * acos(
                cos(radians(?)) * cos(radians(latitude)) *
                cos(radians(longitude) - radians(?)) +
                sin(radians(?)) * sin(radians(latitude))
            )
        ) AS distance", [$latitude, $longitude, $latitude])
            ->orderBy('distance')
            ->where('status', 1)
            ->first();

        return response()->json([
            'city_id' => $city->id ?? null,
            'city_name' => $city->name ?? null,
        ]);
    }
}
